from bson import ObjectId
from dataclasses import dataclass

@dataclass
class NotificationChannels:
    email: bool
    sms: bool
    inapp: bool

@dataclass
class NotificationDto:
    channels: NotificationChannels
    templateId: str
    subject: str
    organization: ObjectId
    inAppTopic: str
    phoneNumber:  str | None
    email: str
    data: dict
        
    def to_dict(self) -> dict:
        """Convert the DTO to a dictionary for JSON serialization"""
        return {
            'channels': vars(self.channels),
            'templateId': self.templateId,
            'subject': self.subject,
            'organization': str(self.organization),
            'inAppTopic': self.inAppTopic,
            'phoneNumber': self.phoneNumber,
            'email': self.email,
            'data': self.data
        }
from db.DatabaseConnection import DatabaseConnection
from entities.SystemNotification import SystemNotification
from typing import List, Optional
from bson import ObjectId

class SystemNotificationRepository:
    """
    Repository class for handling SystemNotification entity operations.
    """
    
    def __init__(self):
        self.db_connection = DatabaseConnection()
        self.collection = self.db_connection.get_collection('system_notifications')
    
    async def save(self, notification: SystemNotification):
        """
        Save a system notification to the database.
        
        Args:
            notification: SystemNotification entity to save
            
        Returns:
            InsertOneResult: Result of the insert operation
        """
        return self.collection.insert_one(notification.to_entity())
    
    async def find_by_id(self, notification_id: str) -> Optional[dict]:
        """
        Find a system notification by its ID.
        
        Args:
            notification_id: String representation of the notification ID
            
        Returns:
            dict or None: The notification document if found, None otherwise
        """
        return self.collection.find_one({"_id": ObjectId(notification_id)})
    
    async def find_by_org_id(self, org_id: str) -> List[dict]:
        """
        Find all system notifications for a specific organization.
        
        Args:
            org_id: String representation of the organization ID
            
        Returns:
            List[dict]: List of notification documents
        """
        return list(self.collection.find({"org_id": ObjectId(org_id)}))
    
    async def find_active_by_org_id(self, org_id: str) -> List[dict]:
        """
        Find all active system notifications for a specific organization.
        
        Args:
            org_id: String representation of the organization ID
            
        Returns:
            List[dict]: List of active notification documents
        """
        return list(self.collection.find({
            "org_id": ObjectId(org_id),
            "isActive": True
        }))
    
    async def mark_as_seen(self, notification_id: str, user_id: str):
        """
        Mark a notification as seen by a specific user.
        
        Args:
            notification_id: String representation of the notification ID
            user_id: String representation of the user ID
        """
        self.collection.update_one(
            {"_id": ObjectId(notification_id)},
            {"$addToSet": {"seen_by": ObjectId(user_id)}}
        )
    
    async def deactivate(self, notification_id: str):
        """
        Deactivate a system notification.
        
        Args:
            notification_id: String representation of the notification ID
        """
        self.collection.update_one(
            {"_id": ObjectId(notification_id)},
            {"$set": {"isActive": False}}
        )

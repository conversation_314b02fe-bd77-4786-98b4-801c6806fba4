from db.DatabaseConnection import DatabaseConnection
from entities.Notification import Notification
from typing import List, Optional
from bson import ObjectId

class NotificationRepository:
    """
    Repository class for handling Notification entity operations.
    """
    
    def __init__(self):
        self.db_connection = DatabaseConnection()
        self.collection = self.db_connection.get_collection('notifications')
    
    def save(self, notification: dict):
        """
        Save a notification to the database.
        
        Args:
            notification: Notification dictionary to save
            
        Returns:
            InsertOneResult: Result of the insert operation
        """
        return self.collection.insert_one(notification)
    
    def find_by_id(self, notification_id: str) -> Optional[dict]:
        """
        Find a notification by its ID.
        
        Args:
            notification_id: String representation of the notification ID
            
        Returns:
            dict or None: The notification document if found, None otherwise
        """
        return self.collection.find_one({"_id": ObjectId(notification_id)})
    
    def find_by_org_id(self, org_id: str) -> List[dict]:
        """
        Find all notifications for a specific organization.
        
        Args:
            org_id: String representation of the organization ID
            
        Returns:
            List[dict]: List of notification documents
        """
        return list(self.collection.find({"org_id": ObjectId(org_id)}))
    
    def find_by_event_id(self, event_id: str) -> List[dict]:
        """
        Find all notifications for a specific event.
        
        Args:
            event_id: String representation of the event ID
            
        Returns:
            List[dict]: List of notification documents
        """
        return list(self.collection.find({"event_id": event_id}))
    
    def find_recent_by_alert_zone_and_event(self, alert_zone_id: str, event_id: str, hours_back: int = 6) -> List[dict]:
        """
        Find recent notifications for a specific alert zone and event.
        
        Args:
            alert_zone_id: String representation of the alert zone ID
            event_id: String representation of the event ID
            hours_back: Number of hours to look back (default: 6)
            
        Returns:
            List[dict]: List of notification documents
        """
        return list(self.collection.find({
            "alertZone._id": alert_zone_id,
            "event_id": event_id,
            "timestamp": {
                "$gte": {"$subtract": [{"$toLong": "$$NOW"}, hours_back * 3600000]}
            }
        }))
    
    def mark_as_seen(self, notification_id: str):
        """
        Mark a notification as seen.
        
        Args:
            notification_id: String representation of the notification ID
        """
        self.collection.update_one(
            {"_id": ObjectId(notification_id)},
            {"$set": {"seen": True}}
        )
    
    def delete_by_id(self, notification_id: str):
        """
        Delete a notification by its ID.
        
        Args:
            notification_id: String representation of the notification ID
        """
        self.collection.delete_one({"_id": ObjectId(notification_id)})
    
    def find_unseen_by_org_id(self, org_id: str) -> List[dict]:
        """
        Find all unseen notifications for a specific organization.
        
        Args:
            org_id: String representation of the organization ID
            
        Returns:
            List[dict]: List of unseen notification documents
        """
        return list(self.collection.find({
            "org_id": ObjectId(org_id),
            "seen": False
        }))

from db.DatabaseConnection import DatabaseConnection
from entities.User import User
from utils.CacheService import CacheService
from typing import List, Optional
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

class UserRepository:
    """
    Repository class for handling User entity operations.
    """

    def __init__(self, enable_cache: bool = True):
        self.db_connection = DatabaseConnection()
        self.collection = self.db_connection.get_collection('users')
        self.user_preferences_collection = self.db_connection.get_collection('userpreferences')
        self.enable_cache = enable_cache
        if self.enable_cache:
            self.cache_service = CacheService()

    def find_for_orgs(self, orgs: str) -> List[User]:
        """
        Find users for specific organizations.

        Args:
            orgs: String representation of organization ID

        Returns:
            List[User]: List of User entities
        """
        logger.debug(f"Finding users for org: {orgs}")

        # Try to get from cache first
        if self.enable_cache:
            cached_users_data = self.cache_service.get_users_by_org(orgs)
            if cached_users_data:
                logger.debug(f"Returning {len(cached_users_data)} cached users for org: {orgs}")
                return [User.from_dict(user_data) for user_data in cached_users_data]

        # Cache miss or caching disabled - query database
        users_cursor = self.collection.aggregate([
            {
                "$match": {
                    "orgs": orgs
                }
            },
            {
                "$lookup": {
                    "from": "userpreferences",
                    "localField": "_id",
                    "foreignField": "user_id",
                    "as": "user_pref"
                }
            }
        ])

        users_data = list(users_cursor)
        users = [User.from_dict(user) for user in users_data]

        # Cache the results
        if self.enable_cache and users_data:
            self.cache_service.set_users_by_org(orgs, users_data)
            logger.debug(f"Cached {len(users_data)} users for org: {orgs}")

        logger.debug(f"Found {len(users)} users for org: {orgs}")
        return users

    def find_by_id(self, user_id: str) -> Optional[dict]:
        """
        Find a user by their ID.

        Args:
            user_id: String representation of the user ID

        Returns:
            dict or None: The user document if found, None otherwise
        """
        return self.collection.find_one({"_id": ObjectId(user_id)})

    def find_by_email(self, email: str) -> Optional[dict]:
        """
        Find a user by their email address.

        Args:
            email: Email address to search for

        Returns:
            dict or None: The user document if found, None otherwise
        """
        return self.collection.find_one({"email": email})

    def find_by_sub(self, sub: str) -> Optional[dict]:
        """
        Find a user by their Auth0 sub identifier.

        Args:
            sub: Auth0 sub identifier

        Returns:
            dict or None: The user document if found, None otherwise
        """
        return self.collection.find_one({"sub": sub})

    def find_all_by_org_id(self, org_id: str) -> List[dict]:
        """
        Find all users belonging to a specific organization.

        Args:
            org_id: String representation of the organization ID

        Returns:
            List[dict]: List of user documents
        """
        return list(self.collection.find({"org_id": org_id}))

    def save(self, user: User):
        """
        Save a user to the database.

        Args:
            user: User entity to save

        Returns:
            InsertOneResult: Result of the insert operation
        """
        return self.collection.insert_one(user.to_entity())

    def update(self, user_id: str, update_data: dict):
        """
        Update a user's information.

        Args:
            user_id: String representation of the user ID
            update_data: Dictionary containing fields to update
        """
        self.collection.update_one(
            {"_id": ObjectId(user_id)},
            {"$set": update_data}
        )

    def delete(self, user_id: str):
        """
        Delete a user by their ID.

        Args:
            user_id: String representation of the user ID
        """
        self.collection.delete_one({"_id": ObjectId(user_id)})

    def find_with_preferences(self, user_id: str) -> Optional[dict]:
        """
        Find a user with their preferences included.

        Args:
            user_id: String representation of the user ID

        Returns:
            dict or None: The user document with preferences if found, None otherwise
        """
        users = list(self.collection.aggregate([
            {
                "$match": {
                    "_id": ObjectId(user_id)
                }
            },
            {
                "$lookup": {
                    "from": "userpreferences",
                    "localField": "_id",
                    "foreignField": "user_id",
                    "as": "user_pref"
                }
            }
        ]))
        return users[0] if users else None

    def find_by_service_zones(self, service_zone_ids: List[str]) -> List[dict]:
        """
        Find users by their assigned service zones.

        Args:
            service_zone_ids: List of service zone ID strings

        Returns:
            List[dict]: List of user documents
        """
        object_ids = [ObjectId(zone_id) for zone_id in service_zone_ids]
        return list(self.collection.find({
            "service_zones": {"$in": object_ids}
        }))

    def invalidate_cache_for_org(self, org_id: str):
        """
        Invalidate cached users for a specific organization.

        Args:
            org_id: String representation of the organization ID
        """
        if self.enable_cache:
            success = self.cache_service.invalidate_users_by_org(org_id)
            if success:
                logger.debug(f"Invalidated user cache for org: {org_id}")
            else:
                logger.warning(f"Failed to invalidate user cache for org: {org_id}")

    def invalidate_user_preferences_cache(self, user_id: str):
        """
        Invalidate cached user preferences for a specific user.

        Args:
            user_id: String representation of the user ID
        """
        if self.enable_cache:
            success = self.cache_service.invalidate_user_preferences(user_id)
            if success:
                logger.debug(f"Invalidated user preferences cache for user: {user_id}")
            else:
                logger.warning(f"Failed to invalidate user preferences cache for user: {user_id}")

    def invalidate_all_user_caches(self):
        """
        Invalidate all user-related caches.
        """
        if self.enable_cache:
            deleted_count = self.cache_service.invalidate_all_user_caches()
            logger.info(f"Invalidated {deleted_count} user cache entries")

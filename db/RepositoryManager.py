from db.repositories.SystemNotificationRepository import SystemNotificationRepository
from db.repositories.NotificationRepository import NotificationRepository
from db.repositories.AlertZoneRepository import AlertZoneRepository
from db.repositories.UserRepository import UserRepository
from db.DatabaseConnection import DatabaseConnection
from utils.CacheService import CacheService
import logging

logger = logging.getLogger(__name__)

class RepositoryManager:
    """
    Manager class that provides access to all repositories with a single database connection.
    This is the recommended way to access repositories in new code.
    """

    def __init__(self, enable_cache: bool = True):
        # Ensure single database connection across all repositories
        self.db_connection = DatabaseConnection()
        self.enable_cache = enable_cache

        # Initialize cache service if caching is enabled
        if self.enable_cache:
            self.cache_service = CacheService()
            # Test cache connection
            if not self.cache_service.test_connection():
                logger.warning("Cache connection failed, disabling cache for this session")
                self.enable_cache = False

        # Initialize all repositories
        self._system_notification_repo = None
        self._notification_repo = None
        self._alert_zone_repo = None
        self._user_repo = None

    @property
    def system_notifications(self) -> SystemNotificationRepository:
        """Get the SystemNotificationRepository instance."""
        if self._system_notification_repo is None:
            self._system_notification_repo = SystemNotificationRepository()
        return self._system_notification_repo

    @property
    def notifications(self) -> NotificationRepository:
        """Get the NotificationRepository instance."""
        if self._notification_repo is None:
            self._notification_repo = NotificationRepository()
        return self._notification_repo

    @property
    def alert_zones(self) -> AlertZoneRepository:
        """Get the AlertZoneRepository instance."""
        if self._alert_zone_repo is None:
            self._alert_zone_repo = AlertZoneRepository(enable_cache=self.enable_cache)
        return self._alert_zone_repo

    @property
    def users(self) -> UserRepository:
        """Get the UserRepository instance."""
        if self._user_repo is None:
            self._user_repo = UserRepository(enable_cache=self.enable_cache)
        return self._user_repo

    def close_connection(self):
        """Close the database connection."""
        self.db_connection.close_connection()

    def get_collection(self, collection_name: str):
        """
        Get a collection directly from the database.
        Use this for operations not covered by the repositories.

        Args:
            collection_name: Name of the collection to retrieve

        Returns:
            MongoDB collection object
        """
        return self.db_connection.get_collection(collection_name)

    def invalidate_all_caches(self):
        """
        Invalidate all caches across all repositories.
        """
        if not self.enable_cache:
            logger.info("Caching is disabled, no cache to invalidate")
            return

        total_deleted = 0

        # Invalidate user caches
        if self._user_repo:
            self._user_repo.invalidate_all_user_caches()
        else:
            total_deleted += self.cache_service.invalidate_all_user_caches()

        # Invalidate alert zone caches
        if self._alert_zone_repo:
            self._alert_zone_repo.invalidate_all_caches()
        else:
            total_deleted += self.cache_service.invalidate_all_alert_zone_caches()

        logger.info(f"Invalidated all caches, total entries: {total_deleted}")

    def get_cache_stats(self):
        """
        Get cache statistics.

        Returns:
            dict: Cache statistics or empty dict if caching is disabled
        """
        if not self.enable_cache:
            return {"caching_enabled": False}

        stats = self.cache_service.get_cache_stats()
        stats["caching_enabled"] = True
        return stats

    def invalidate_caches_for_org(self, org_id: str):
        """
        Invalidate all caches related to a specific organization.

        Args:
            org_id: String representation of the organization ID
        """
        if not self.enable_cache:
            return

        # Invalidate user caches for this org
        if self._user_repo:
            self._user_repo.invalidate_cache_for_org(org_id)
        else:
            self.cache_service.invalidate_users_by_org(org_id)

        logger.debug(f"Invalidated caches for organization: {org_id}")

    def invalidate_caches_for_event(self, event_id: str):
        """
        Invalidate all caches related to a specific event.

        Args:
            event_id: String representation of the event ID
        """
        if not self.enable_cache:
            return

        # Invalidate alert zone geo caches for this event
        if self._alert_zone_repo:
            self._alert_zone_repo.invalidate_geo_cache_for_event(event_id)
        else:
            self.cache_service.invalidate_alert_zones_geo_pattern(event_id)

        logger.debug(f"Invalidated caches for event: {event_id}")

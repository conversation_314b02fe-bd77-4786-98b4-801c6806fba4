#!/bin/bash

export PATH=$(brew --prefix kafka)/bin:$PATH

export CLASSPATH='aws-msk-iam-auth-2.2.0-all.jar'

for i in {1..1}; do
    cat message.txt | kafka-console-producer \
    --bootstrap-server 'b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198' \
    --topic $1 \
    --producer.config 'client.properties' 
    echo "Message $i sent"
done

# kafka-console-producer \
#     --bootstrap-server 'b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198' \
#     --topic NOTIFICATION_DEV \
#     --producer.config 'client.properties' \
#     --property parse.key=true \
#     --property key.separator=:
#!/bin/bash

export PATH=$(brew --prefix kafka)/bin:$PATH

export CLASSPATH='aws-msk-iam-auth-2.2.0-all.jar'

kafka-topics \
--bootstrap-server 'b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198' \
--topic $1 \
--create \
--partitions 50 \
--replication-factor 3 \
--command-config client.properties
import datetime
import json
import os
import logging
from typing import List, Dict, Any, Optional
from bson import ObjectId
from entities.Notification import Notification, LightAlertZone
from dtos.NotificationDto import NotificationDto, NotificationChannels
from entities.User import User
from entities.Event import Event
from validations.UserPrefsValidation import UserPrefsValidation
from db.RepositoryManager import RepositoryManager

logger = logging.getLogger(__name__)

class AlertDistributerEvent:
    TOPIC_NAME = os.getenv("NOTIFICATION_TOPIC", "NOTIFICATION_DEV")
    ENV_URL = os.getenv("ENV_URL", "https://dev.aerodefense.tech")

    def __init__(self, repository_manager: 'RepositoryManager', producer, app_sync_handler):
        self.producer = producer
        self.repo_manager = repository_manager
        self.app_sync_handler = app_sync_handler
        self.tracked_alert_zones: Dict[str, List[Dict[str, Any]]] = {}
        self.user_prefs_validation = UserPrefsValidation()

        logger.info("AlertDistributerEvent initialized successfully")


    async def distribute_event_notification(self, event_data: Dict[str, Any]) -> None:
        try:
            logger.info(f"Processing event notification: {event_data.get('EVENT_ID', 'Unknown')}")

            event = self._create_event_from_data(event_data)
            drone_location = event.get_drone_location()
            alert_zones = self._load_alert_zones(event, drone_location)
            authorized_drones = self._get_authorized_drones(event_data)

            logger.info(f"Found {len(alert_zones)} alert zones for event {event.event_id}")

            for alert_zone in alert_zones:
                await self._process_alert_zone_notification(event, alert_zone)

        except Exception as e:
            logger.error(f"Error distributing event notification: {e}", exc_info=True)
            raise

    def _create_event_from_data(self, event_data: Dict[str, Any]) -> Event:
        try:
            if not event_data:
                raise ValueError("Event data cannot be empty")

            event = Event.from_dict(event_data)

            if not event.event_id:
                raise ValueError("Event ID is required")

            return event

        except Exception as e:
            logger.error(f"Failed to create event from data: {e}")
            raise ValueError(f"Invalid event data: {e}")
        
    async def _get_authorized_drones(self, device_id):
        try:
            authorized_drones = self.repo_manager.authorized_drones.find_by_device_id(device_id)
            return authorized_drones
        except Exception as e:
            logger.error(f"Failed to get authorized drones: {e}")
            raise

    async def _process_alert_zone_notification(self, event: Event, alert_zone: Dict[str, Any]) -> None:
        try:
            org_auth_id = alert_zone['organization'][0]['auth0_id']
            notification_count = len(alert_zone.get('notifications', []))

            logger.debug(f"Processing alert zone {org_auth_id} with {notification_count} existing notifications")

            notification_data = self._prepare_drone_detection_data(event, alert_zone)

            if event.is_complete():
                await self._send_completion_notification(org_auth_id, notification_data)
            elif notification_count == 0:
                await self._process_new_alert_notification(event, alert_zone, notification_data)

        except Exception as e:
            logger.error(f"Error processing alert zone notification: {e}", exc_info=True)

    async def _send_completion_notification(self, org_auth_id: str, notification_data: Dict[str, Any]) -> None:
        try:
            await self.app_sync_handler.publish_message(org_auth_id, notification_data)
            logger.info(f"Sent completion notification to organization {org_auth_id}")
        except Exception as e:
            logger.error(f"Failed to send completion notification to {org_auth_id}: {e}")

    async def _process_new_alert_notification(self, event: Event, alert_zone: Dict[str, Any], notification_data: Dict[str, Any]) -> None:
        try:
            if alert_zone.get('isActive', False):
                org_auth_id = alert_zone['organization'][0]['auth0_id']
                await self.app_sync_handler.publish_message(org_auth_id, notification_data)

            await self._save_organization_notification(alert_zone, event)
            await self._notify_users_in_organization(alert_zone, event)

        except Exception as e:
            logger.error(f"Error processing new alert notification: {e}", exc_info=True)

    async def _notify_users_in_organization(self, alert_zone: Dict[str, Any], event: Event) -> None:
        try:
            org_auth_id = str(alert_zone['organization'][0]['auth0_id'])
            users = self.repo_manager.users.find_for_orgs(org_auth_id)

            logger.info(f"Notifying {len(users)} users in organization {org_auth_id}")

            for user in users:
                await self._send_user_notification(alert_zone, event, user)

        except Exception as e:
            logger.error(f"Error notifying users in organization: {e}", exc_info=True)


    def _load_alert_zones(self, event: Event, drone_location: Dict[str, Any]) -> List[Dict[str, Any]]:
        try:
            if event.is_complete():
                return self._handle_completed_event_zones(event)
            else:
                return self._handle_active_event_zones(event, drone_location)

        except Exception as e:
            logger.error(f"Error loading alert zones for event {event.event_id}: {e}")
            return []

    def _handle_completed_event_zones(self, event: Event) -> List[Dict[str, Any]]:
        try:
            if event.event_id in self.tracked_alert_zones:
                alert_zones = self.tracked_alert_zones[event.event_id]
                try:
                    del self.tracked_alert_zones[event.event_id]
                    logger.info(f"Removed completed event {event.event_id} from tracking")
                except KeyError:
                    logger.warning(f"Event {event.event_id} was already removed from tracking")
                return alert_zones
            else:
                logger.warning(f"Completed event {event.event_id} not found in tracked zones")
                self.tracked_alert_zones.clear()
                return []

        except Exception as e:
            logger.error(f"Error handling completed event zones: {e}")
            return []

    def _handle_active_event_zones(self, event: Event, drone_location: Dict[str, Any]) -> List[Dict[str, Any]]:
        try:
            alert_zones = self.repo_manager.alert_zones.find_with_users_to_send(drone_location, event.event_id)

            if event.event_id not in self.tracked_alert_zones:
                self.tracked_alert_zones[event.event_id] = alert_zones
                logger.info(f"Started tracking {len(alert_zones)} zones for event {event.event_id}")
            else:
                new_zones = self._merge_new_alert_zones(event.event_id, alert_zones)
                logger.info(f"Added {len(new_zones)} new zones for event {event.event_id}")

            return alert_zones

        except Exception as e:
            logger.error(f"Error handling active event zones: {e}")
            return []

    def _merge_new_alert_zones(self, event_id: str, new_alert_zones: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        try:
            existing_zone_ids = {zone['_id'] for zone in self.tracked_alert_zones[event_id]}
            new_zones = [zone for zone in new_alert_zones if zone['_id'] not in existing_zone_ids]
            self.tracked_alert_zones[event_id].extend(new_zones)
            return new_zones

        except Exception as e:
            logger.error(f"Error merging alert zones for event {event_id}: {e}")
            return []

    async def _save_organization_notification(self, alert_zone: Dict[str, Any], event: Event) -> Optional[Notification]:
        try:
            org_id = alert_zone['orgId']
            light_alert_zone = LightAlertZone(
                _id=alert_zone['_id'],
                name=alert_zone['name']
            )

            notification = Notification(
                org_id=ObjectId(org_id),
                alertZone=light_alert_zone,
                event_id=event.event_id,
                timestamp=datetime.datetime.now(datetime.timezone.utc),
                seen=False,
                type="ALERT"
            )

            logger.info(f"Saving notification for organization {org_id}, event {event.event_id}")
            self.repo_manager.notifications.save(notification.to_entity())

            return notification

        except Exception as e:
            logger.error(f"Failed to save organization notification: {e}", exc_info=True)
            return None

    async def _send_user_notification(self, alert_zone: Dict[str, Any], event: Event, user: User) -> None:
        try:
            notification = self._prepare_user_notification(alert_zone, event, user)

            if self._should_send_external_notification(notification):
                if alert_zone.get('isActive', False):
                    await self._send_external_notification(notification)

        except Exception as e:
            logger.error(f"Error sending user notification to {user.email}: {e}", exc_info=True)

    def _prepare_user_notification(self, alert_zone: Dict[str, Any], event: Event, user: User) -> NotificationDto:
        try:
            is_email_alert = self.user_prefs_validation.validateEmailAlert(user)
            is_sms_alert = self.user_prefs_validation.validateSmsAlert(user)

            logger.debug(f"User {user.email} preferences - email: {is_email_alert}, sms: {is_sms_alert}")

            notification_channels = NotificationChannels(
                email=is_email_alert,
                sms=is_sms_alert,
                inapp=True
            )

            notification_data = self._prepare_drone_detection_data(event, alert_zone)

            notification = NotificationDto(
                channels=notification_channels,
                templateId="drone-alert",
                subject="Drone Alert",
                organization=alert_zone['orgId'],
                inAppTopic=user.sub,
                phoneNumber=user.phone_number,
                email=user.email,
                data=notification_data
            )

            return notification

        except Exception as e:
            logger.error(f"Error preparing user notification for {user.email}: {e}")
            raise

    def _should_send_external_notification(self, notification: NotificationDto) -> bool:
        return notification.channels.email or notification.channels.sms

    async def _send_external_notification(self, notification: NotificationDto) -> None:
        try:
            message_data = json.dumps(notification.to_dict())
            await self.producer.produce_message(
                self.TOPIC_NAME,
                message_data,
                notification.inAppTopic
            )
            logger.info(f"Sent external notification to {notification.email}")

        except Exception as e:
            logger.error(f"Failed to send external notification to {notification.email}: {e}")

    def _prepare_drone_detection_data(self, event: Event, alert_zone: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Get basic event data
            uas_type = event.get_uas_type()
            location_status = event.get_location_status()
            operator_type = event.get_operator_type()

            # Get operator location
            operator_location = event.get_operator_location()
            operator_lat = operator_location.get('lat', 0)
            operator_lon = operator_location.get('lng', 0)

            # Get drone location
            drone_location = event.get_drone_location()

            # Get drone heading with safe fallback
            drone_heading = self._get_safe_drone_heading(event)

            # Get organization name safely
            org_name = self._get_organization_name(alert_zone)

            return {
                "alertZoneId": alert_zone['_id'],
                "alertZoneName": alert_zone['name'],
                "currentDate": datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S"),
                "eventId": event.event_id,
                "uasId": event.uas_id,
                "uasType": uas_type,
                "droneLat": drone_location.get('lat', 0),
                "droneLng": drone_location.get('lng', 0),
                "pilotLat": operator_lat,
                "pilotLng": operator_lon,
                "locationStatus": location_status,
                "altitude": event.get_drone_altitude(),
                "speed": event.get_drone_speed(),
                "heading": drone_heading,
                "operatorType": operator_type,
                "complete": event.complete,
                "orgName": org_name,
                "event": event.to_dict(),
                "envUrl": self.ENV_URL
            }

        except Exception as e:
            logger.error(f"Error preparing drone detection data: {e}", exc_info=True)
            raise

    def _get_safe_drone_heading(self, event: Event) -> str:
        try:
            return event.get_drone_heading()
        except Exception as e:
            logger.warning(f"Could not get drone heading for event {event.event_id}: {e}")
            return 'N/A'

    def _get_organization_name(self, alert_zone: Dict[str, Any]) -> str:
        try:
            return alert_zone['organization'][0]['name']
        except (KeyError, IndexError, TypeError) as e:
            logger.warning(f"Could not get organization name from alert zone: {e}")
            return 'Unknown Organization'

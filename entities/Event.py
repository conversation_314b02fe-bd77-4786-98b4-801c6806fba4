from bson import ObjectId
from typing import List, Dict, Any, Optional, Union
import datetime

class Event:
    """
    Event entity representing a drone detection event in the system.
    This class maps to the events collection in MongoDB.
    """
    def __init__(self,
                 event_id: str,
                 service_list: List[str],
                 device_id: str,
                 uas_id: str,
                 operator_id: str,
                 h3_list: List[str],
                 node_list: List[str],
                 start_time: str,
                 end_time: str,
                 duration: float,
                 manufacturer: Optional[str] = None,
                 device_type: int = 0,
                 complete: int = 0,
                 info: Optional[Dict[str, Any]] = None,
                 _id: Optional[ObjectId] = None):
        self._id = _id
        self.event_id = event_id
        self.service_list = service_list
        self.device_id = device_id
        self.uas_id = uas_id
        self.operator_id = operator_id
        self.h3_list = h3_list
        self.node_list = node_list
        self.start_time = start_time
        self.end_time = end_time
        self.duration = duration
        self.manufacturer = manufacturer
        self.device_type = device_type
        self.complete = complete
        self.info = info or {}

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create an Event instance from a dictionary (e.g., from MongoDB)"""
        complete_value = data.get('COMPLETE', 0)
        return cls(
            _id=data.get('_id'),
            event_id=data.get('EVENT_ID'),
            service_list=data.get('SERVICE_LIST', []),
            device_id=data.get('DEVICE_ID'),
            uas_id=data.get('UAS_ID'),
            operator_id=data.get('OPERATOR_ID'),
            h3_list=data.get('H3_LIST', []),
            node_list=data.get('NODE_LIST', []),
            start_time=data.get('START_TIME'),
            end_time=data.get('END_TIME'),
            duration=data.get('DURATION'),
            manufacturer=data.get('MANUFACTURER'),
            device_type=data.get('DEVICE_TYPE', 0),
            complete= int(complete_value) if isinstance(complete_value, str) else complete_value,
            info=data.get('INFO', {})
        )

    def to_entity(self) -> Dict[str, Any]:
        """Convert the Event to a dictionary for database storage"""
        return {
            "_id": self._id,
            "EVENT_ID": self.event_id,
            "SERVICE_LIST": self.service_list,
            "DEVICE_ID": self.device_id,
            "UAS_ID": self.uas_id,
            "OPERATOR_ID": self.operator_id,
            "H3_LIST": self.h3_list,
            "NODE_LIST": self.node_list,
            "START_TIME": self.start_time,
            "END_TIME": self.end_time,
            "DURATION": self.duration,
            "MANUFACTURER": self.manufacturer,
            "DEVICE_TYPE": self.device_type,
            "COMPLETE": self.complete,
            "INFO": self.info
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert the Event to a dictionary for JSON serialization"""
        return {
            "_id": str(self._id) if self._id else None,
            "EVENT_ID": self.event_id,
            "SERVICE_LIST": self.service_list,
            "DEVICE_ID": self.device_id,
            "UAS_ID": self.uas_id,
            "OPERATOR_ID": self.operator_id,
            "H3_LIST": self.h3_list,
            "NODE_LIST": self.node_list,
            "START_TIME": self.start_time,
            "END_TIME": self.end_time,
            "DURATION": self.duration,
            "MANUFACTURER": self.manufacturer,
            "DEVICE_TYPE": self.device_type,
            "COMPLETE": self.complete,
            "INFO": self.info
        }

    def get_drone_location(self) -> Dict[str, float]:
        """Get the drone location from the info field"""
        if self.info and 'LAT' in self.info and 'LON' in self.info:
            return {
                'lat': self.info['LAT'],
                'lng': self.info['LON']
            }
        return {'lat': 0.0, 'lng': 0.0}

    def get_drone_altitude(self) -> float:
        """Get the drone altitude from the INFO field"""
        if self.info and 'ALTITUDE' in self.info:
            return self.info['ALTITUDE']
        return 0.0

    def get_drone_speed(self) -> float:
        """Get the drone speed from the INFO field"""
        if self.info and 'SPEED' in self.info:
            return self.info['SPEED']
        return 0.0

    def get_drone_heading(self) -> Union[str, float]:
        """Get the drone heading from the INFO field"""
        if self.info and 'INFO' in self.info and 'ODID_loc_direction' in self.info['INFO']:
            return self.info['INFO']['ODID_loc_direction']
        return 'N/A'

    def get_operator_location(self) -> Dict[str, Union[float, str]]:
        """Get the operator location from the INFO field"""
        result = {'lat': 'N/A', 'lng': 'N/A'}
        if self.info and 'INFO' in self.info:
            info = self.info['INFO']
            if 'ODID_system_lat' in info:
                result['lat'] = info['ODID_system_lat']
            if 'ODID_system_lon' in info:
                result['lng'] = info['ODID_system_lon']
        return result

    def get_uas_type(self) -> str:
        """Get the UAS type from the INFO field"""
        if self.info and 'INFO' in self.info and 'ODID_basicID_uaType' in self.info['INFO']:
            from common.OpenDroneIDstandards import OpenDroneID_basicID_uaType
            try:
                return OpenDroneID_basicID_uaType[self.info['INFO']['ODID_basicID_uaType']]
            except (KeyError, TypeError):
                pass
        return 'N/A'

    def get_location_status(self) -> str:
        """Get the location status from the INFO field"""
        if self.info and 'INFO' in self.info and 'ODID_loc_status' in self.info['INFO']:
            from common.OpenDroneIDstandards import OpenDroneID_loc_status
            try:
                return OpenDroneID_loc_status[self.info['INFO']['ODID_loc_status']]
            except (KeyError, TypeError):
                return self.info['INFO']['ODID_loc_status']
        return 'N/A'

    def get_operator_type(self) -> str:
        """Get the operator type from the INFO field"""
        if self.info and 'INFO' in self.info and 'ODID_operator_type' in self.info['INFO']:
            from common.OpenDroneIDstandards import OpenDroneID_operator_type
            try:
                return OpenDroneID_operator_type[self.info['INFO']['ODID_operator_type']]
            except (KeyError, TypeError):
                pass
        return 'N/A'

    def get_start_time_datetime(self) -> Optional[datetime.datetime]:
        """Parse the start_time string to a datetime object"""
        try:
            if self.start_time:
                # Remove the timezone part if present
                timestamp = self.start_time
                if '+' in timestamp:
                    timestamp = timestamp.split('+')[0]
                return datetime.datetime.fromisoformat(timestamp)
        except (ValueError, TypeError):
            pass
        return None

    def get_end_time_datetime(self) -> Optional[datetime.datetime]:
        """Parse the end_time string to a datetime object"""
        try:
            if self.end_time:
                # Remove the timezone part if present
                timestamp = self.end_time
                if '+' in timestamp:
                    timestamp = timestamp.split('+')[0]
                return datetime.datetime.fromisoformat(timestamp)
        except (ValueError, TypeError):
            pass
        return None

    def get_formatted_start_time(self, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """Get the formatted start time"""
        dt = self.get_start_time_datetime()
        if dt:
            return dt.strftime(format_str)
        return self.start_time

    def get_formatted_end_time(self, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """Get the formatted end time"""
        dt = self.get_end_time_datetime()
        if dt:
            return dt.strftime(format_str)
        return self.end_time

    def is_complete(self) -> bool:
        """Check if the event is complete"""
        return self.complete == 1

    def get_node_info(self) -> Dict[str, Any]:
        """Get the node info from the info field"""
        if self.info and 'node_info' in self.info:
            return self.info['node_info']
        return {}

    def get_time_stamp(self) -> str:
        """Get the time stamp from the info field"""
        if self.info and 'TIME_STAMP' in self.info:
            return self.info['TIME_STAMP']
        return self.end_time

    def get_detection_data(self) -> Dict[str, Any]:
        """
        Get a dictionary with all the relevant detection data for notifications
        """
        try:
            uas_type = self.get_uas_type()
        except:
            uas_type = 'N/A'

        try:
            location_status = self.get_location_status()
        except:
            location_status = 'N/A'

        try:
            operator_type = self.get_operator_type()
        except:
            operator_type = 'N/A'

        operator_location = self.get_operator_location()
        drone_location = self.get_drone_location()

        return {
            "eventId": self.event_id,
            "uasId": self.uas_id,
            "uasType": uas_type,
            "droneLat": drone_location['lat'],
            "droneLng": drone_location['lng'],
            "pilotLat": operator_location['lat'],
            "pilotLng": operator_location['lng'],
            "locationStatus": location_status,
            "altitude": self.get_drone_altitude(),
            "speed": self.get_drone_speed(),
            "heading": self.get_drone_heading(),
            "operatorType": operator_type,
            "complete": self.complete,
            "currentDate": datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S"),
            "event": self.to_dict()
        }
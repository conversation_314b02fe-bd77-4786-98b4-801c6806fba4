from bson import ObjectId
import datetime
from dataclasses import dataclass

@dataclass
class LightAlertZone:
    _id: str
    name: str

    def to_dict(self):
        return {
            "_id": self._id,
            "name": self.name
        }
    
@dataclass
class Notification:
    org_id: ObjectId
    alertZone: LightAlertZone
    event_id: str
    timestamp: datetime
    seen: bool
    type: str
    
    def to_entity(self):
        return {
            "org_id": self.org_id,
            "alertZone": self.alertZone.to_dict(),
            "event_id": self.event_id,
            "timestamp": self.timestamp, 
            "seen": self.seen,
            "type": self.type
        }

    def to_dict(self):
        return {
            "org_id": str(self.org_id),
            "alertZone": self.alertZone.to_dict(),
            "event_id": self.event_id,
            "timestamp": self.timestamp, 
            "seen": self.seen,
            "type": self.type
        }
from bson import ObjectId
from common.enums.NotificationTypes import NotificationTypeEnum
from typing import List
import datetime

class SystemNotification:
    _id: ObjectId
    org_id: ObjectId
    title: str
    description: str
    type: NotificationTypeEnum 
    seen_by: List[ObjectId]
    icon: str
    isActive: bool
    createdAt: datetime
    createdBy: str
    metaData: dict = {}

    def to_entity(self):
        """Convert the SystemNotification to a dictionary for database storage"""
        return {
            "org_id": self.org_id,
            "title": self.title,
            "description": self.description,
            "type": self.type.value[0] if isinstance(self.type.value, tuple) else self.type.value,
            "seen_by": self.seen_by,
            "icon": self.icon,
            "isActive": self.isActive,
            "createdAt": self.createdAt,
            "createdBy": self.createdBy,
            "meta_data": self.metaData
        }

    def to_dict(self):
        """Convert the SystemNotification to a dictionary for JSON serialization"""
        return {
            "_id": str(self._id),
            "org_id": str(self.org_id),
            "title": self.title,
            "description": self.description,
            "type": self.type.value[0] if isinstance(self.type.value, tuple) else self.type.value,
            "seen_by": [str(user_id) for user_id in self.seen_by],
            "icon": self.icon,
            "isActive": self.isActive,
            "createdAt": self.createdAt.isoformat() if isinstance(self.createdAt, datetime.datetime) else self.createdAt,
            "createdBy": self.createdBy,
            "meta_data": self.metaData
        }

from bson import ObjectId
from typing import Dict, Any, Optional

class UserPrefs:
    """
    UserPrefs entity representing user preferences in the system.
    This class maps to the 'userpreferences' collection in MongoDB.
    """
    def __init__(self,
                 user_id: Optional[ObjectId] = None,
                 emailAlert: bool = False,
                 loadInitialPageOnRegionView: bool = True,
                 showAlertZones: bool = True,
                 showDroneTraffic: bool = True,
                 showSateliteView: bool = True,
                 smsAlert: bool = False,
                 _id: Optional[ObjectId] = None,
                 __v: Optional[int] = None):
        self._id = _id
        self.user_id = user_id
        self.emailAlert = emailAlert
        self.loadInitialPageOnRegionView = loadInitialPageOnRegionView
        self.showAlertZones = showAlertZones
        self.showDroneTraffic = showDroneTraffic
        self.showSateliteView = showSateliteView
        self.smsAlert = smsAlert

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create a UserPrefs instance from a dictionary (e.g., from MongoDB)"""
        return cls(
            _id=data.get('_id'),
            user_id=data.get('user_id'),
            emailAlert=data.get('emailAlert', False),
            loadInitialPageOnRegionView=data.get('loadInitialPageOnRegionView', True),
            showAlertZones=data.get('showAlertZones', True),
            showDroneTraffic=data.get('showDroneTraffic', True),
            showSateliteView=data.get('showSateliteView', True),
            smsAlert=data.get('smsAlert', False)
        )

    def to_entity(self) -> Dict[str, Any]:
        """Convert the UserPrefs to a dictionary for database storage"""
        return {
            "_id": self._id,
            "user_id": self.user_id,
            "emailAlert": self.emailAlert,
            "loadInitialPageOnRegionView": self.loadInitialPageOnRegionView,
            "showAlertZones": self.showAlertZones,
            "showDroneTraffic": self.showDroneTraffic,
            "showSateliteView": self.showSateliteView,
            "smsAlert": self.smsAlert
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert the UserPrefs to a dictionary for JSON serialization"""
        return {
            "_id": str(self._id) if self._id else None,
            "user_id": str(self.user_id) if self.user_id else None,
            "emailAlert": self.emailAlert,
            "loadInitialPageOnRegionView": self.loadInitialPageOnRegionView,
            "showAlertZones": self.showAlertZones,
            "showDroneTraffic": self.showDroneTraffic,
            "showSateliteView": self.showSateliteView,
            "smsAlert": self.smsAlert
        }
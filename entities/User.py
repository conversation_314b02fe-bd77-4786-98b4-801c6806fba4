
from bson import ObjectId
from typing import List, Dict, Any, Optional
from entities.UserPrefs import UserPrefs
from entities.Role import Role

class User:
    """
    User entity representing a user in the system.
    This class maps to the 'users' collection in MongoDB.
    """
    def __init__(self,
                 sub: Optional[str] = None,
                 email: Optional[str] = None,
                 email_verified: bool = False,
                 name: Optional[str] = None,
                 nickname: Optional[str] = None,
                 org_id: Optional[str] = None,
                 orgs: Optional[List[str]] = None,
                 phone_number: Optional[str] = None,
                 phone_verified: bool = False,
                 picture: Optional[str] = None,
                 roles: Optional[List[Role]] = None,
                 service_zones: Optional[List[ObjectId]] = None,
                 updated_at: Optional[str] = None,
                 family_name: Optional[str] = None,
                 given_name: Optional[str] = None,
                 last_activity: Optional[str] = None,
                 created_at: Optional[str] = None,
                 user_pref: Optional[List[UserPrefs]] = None,
                 _id: Optional[ObjectId] = None):
        self._id = _id
        self.sub = sub
        self.email = email
        self.email_verified = email_verified
        self.name = name
        self.nickname = nickname
        self.org_id = org_id
        self.orgs = orgs or []
        self.phone_number = phone_number
        self.phone_verified = phone_verified
        self.picture = picture
        self.roles = roles or []
        self.service_zones = service_zones or []
        self.updated_at = updated_at
        self.family_name = family_name
        self.given_name = given_name
        self.last_activity = last_activity
        self.created_at = created_at
        self.user_pref = user_pref or []

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create a User instance from a dictionary (e.g., from MongoDB)"""
        return cls(
            _id=data.get('_id'),
            sub=data.get('sub'),
            email=data.get('email'),
            email_verified=data.get('email_verified', False),
            name=data.get('name'),
            nickname=data.get('nickname'),
            org_id=data.get('org_id'),
            orgs=data.get('orgs', []),
            phone_number=data.get('phone_number'),
            phone_verified=data.get('phone_verified', False),
            picture=data.get('picture'),
            roles=[Role.from_dict(role) for role in data.get('roles', [])],
            service_zones=data.get('service_zones', []),
            updated_at=data.get('updated_at'),
            family_name=data.get('family_name'),
            given_name=data.get('given_name'),
            last_activity=data.get('last_activity'),
            created_at=data.get('created_at'),
            user_pref=[UserPrefs.from_dict(pref) for pref in data.get('user_pref', [])]
        )

    def to_entity(self) -> Dict[str, Any]:
        """Convert the User to a dictionary for database storage"""
        return {
            "_id": self._id,
            "sub": self.sub,
            "email": self.email,
            "email_verified": self.email_verified,
            "name": self.name,
            "nickname": self.nickname,
            "org_id": self.org_id,
            "orgs": self.orgs,
            "phone_number": self.phone_number,
            "phone_verified": self.phone_verified,
            "picture": self.picture,
            "roles": self.roles,
            "service_zones": self.service_zones,
            "updated_at": self.updated_at,
            "family_name": self.family_name,
            "given_name": self.given_name,
            "last_activity": self.last_activity,
            "created_at": self.created_at,
            "user_pref": [pref.to_entity() for pref in self.user_pref] if self.user_pref else []
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert the User to a dictionary for JSON serialization"""
        result = {
            "_id": str(self._id) if self._id else None,
            "sub": self.sub,
            "email": self.email,
            "email_verified": self.email_verified,
            "name": self.name,
            "nickname": self.nickname,
            "org_id": self.org_id,
            "orgs": self.orgs,
            "phone_number": self.phone_number,
            "phone_verified": self.phone_verified,
            "picture": self.picture,
            "roles": self.roles,
            "service_zones": self.service_zones,
            "updated_at": self.updated_at,
            "family_name": self.family_name,
            "given_name": self.given_name,
            "last_activity": self.last_activity,
            "created_at": self.created_at
        }

        # Include user preferences if available
        if self.user_pref:
            result["user_pref"] = [pref.to_dict() for pref in self.user_pref]

        return result
from entities.User import User
import os

class UserPrefsValidation:
    ENVIORNMENT = os.getenv("ENV", "dev")

    def validateEmailAlert (self, user: User):
        if(self.isEnvDevOrQa()):
            return False
        
        if(user.user_pref and len(user.user_pref) > 0):
            return user.user_pref[0].emailAlert
        else:
            return True
        
    def validateSmsAlert (self, user: User):
        if(self.isEnvDevOrQa()):
            return False
        
        if(user.user_pref and len(user.user_pref) > 0):
            return user.user_pref[0].smsAlert
        else:
            return True 
        
    def isEnvDevOrQa(self):
        if(self.ENVIORNMENT in ["dev", "qa"]):
            return True
        else:
            return False
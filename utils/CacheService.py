import hashlib
import logging
from typing import Optional, List, Dict, Any
from utils.CacheManager import CacheManager

logger = logging.getLogger(__name__)

class CacheService:
    def __init__(self):
        self.cache_manager = CacheManager()
        
        # Cache TTL configurations (in seconds)
        self.ttl_config = {
            'users_by_org': 1800,  # 30 minutes
            'alert_zones_geo': 3600,  # 1 hour
            'alert_zones_by_service': 1800,  # 30 minutes
            'organizations': 7200,  # 2 hours
            'user_preferences': 1800,  # 30 minutes
            'notifications': 300,  # 5 minutes
        }

    def _generate_cache_key(self, prefix: str, *args) -> str:
        key_parts = [prefix] + [str(arg) for arg in args]
        key = ":".join(key_parts)
        if len(key) > 200:
            hash_suffix = hashlib.md5(key.encode()).hexdigest()[:8]
            key = f"{prefix}:hash:{hash_suffix}"
        return key

    def get_users_by_org(self, org_id: str) -> Optional[List[Dict]]:
        cache_key = self._generate_cache_key("users_by_org", org_id)
        cached_users = self.cache_manager.getList(cache_key)
        if cached_users:
            logger.debug(f"Cache hit for users in org: {org_id}")
            return cached_users
        logger.debug(f"Cache miss for users in org: {org_id}")
        return None

    def set_users_by_org(self, org_id: str, users: List[Dict]) -> bool:
        cache_key = self._generate_cache_key("users_by_org", org_id)
        ttl = self.ttl_config['users_by_org']
        success = self.cache_manager.setList(cache_key, users, ttl)
        if success:
            logger.debug(f"Cached {len(users)} users for org: {org_id}")
        return success

    def get_alert_zones_geo(self, lng: float, lat: float, event_id: str) -> Optional[List[Dict]]:
        cache_key = self._generate_cache_key("alert_zones_geo", f"{lng:.6f}", f"{lat:.6f}", event_id)
        cached_zones = self.cache_manager.getList(cache_key)
        if cached_zones:
            logger.debug(f"Cache hit for geo alert zones: {lng}, {lat}, {event_id}")
            return cached_zones
        logger.debug(f"Cache miss for geo alert zones: {lng}, {lat}, {event_id}")
        return None

    def set_alert_zones_geo(self, lng: float, lat: float, event_id: str, zones: List[Dict]) -> bool:
        cache_key = self._generate_cache_key("alert_zones_geo", f"{lng:.6f}", f"{lat:.6f}", event_id)
        ttl = self.ttl_config['alert_zones_geo']
        success = self.cache_manager.setList(cache_key, zones, ttl)
        if success:
            logger.debug(f"Cached {len(zones)} geo alert zones for: {lng}, {lat}, {event_id}")
        return success

    def get_alert_zones_by_service(self, service_id: str) -> Optional[List[Dict]]:
        cache_key = self._generate_cache_key("alert_zones_service", service_id)
        cached_zones = self.cache_manager.getList(cache_key)
        if cached_zones:
            logger.debug(f"Cache hit for service alert zones: {service_id}")
            return cached_zones
        logger.debug(f"Cache miss for service alert zones: {service_id}")
        return None

    def set_alert_zones_by_service(self, service_id: str, zones: List[Dict]) -> bool:
        cache_key = self._generate_cache_key("alert_zones_service", service_id)
        ttl = self.ttl_config['alert_zones_by_service']
        success = self.cache_manager.setList(cache_key, zones, ttl)
        if success:
            logger.debug(f"Cached {len(zones)} service alert zones for: {service_id}")
        return success

    def get_organization(self, org_id: str) -> Optional[Dict]:
        cache_key = self._generate_cache_key("organization", org_id)
        cached_org = self.cache_manager.getJson(cache_key)
        if cached_org:
            logger.debug(f"Cache hit for organization: {org_id}")
            return cached_org
        logger.debug(f"Cache miss for organization: {org_id}")
        return None

    def set_organization(self, org_id: str, organization: Dict) -> bool:
        cache_key = self._generate_cache_key("organization", org_id)
        ttl = self.ttl_config['organizations']
        success = self.cache_manager.setJson(cache_key, organization, ttl)
        if success:
            logger.debug(f"Cached organization: {org_id}")
        return success

    def get_user_preferences(self, user_id: str) -> Optional[Dict]:
        cache_key = self._generate_cache_key("user_prefs", user_id)
        cached_prefs = self.cache_manager.getJson(cache_key)
        if cached_prefs:
            logger.debug(f"Cache hit for user preferences: {user_id}")
            return cached_prefs
        logger.debug(f"Cache miss for user preferences: {user_id}")
        return None

    def set_user_preferences(self, user_id: str, preferences: Dict) -> bool:
        cache_key = self._generate_cache_key("user_prefs", user_id)
        ttl = self.ttl_config['user_preferences']
        success = self.cache_manager.setJson(cache_key, preferences, ttl)
        if success:
            logger.debug(f"Cached user preferences: {user_id}")
        return success

    def invalidate_users_by_org(self, org_id: str) -> bool:
        cache_key = self._generate_cache_key("users_by_org", org_id)
        return self.cache_manager.deleteKey(cache_key)

    def invalidate_alert_zones_geo_pattern(self, event_id: str = None) -> int:
        if event_id:
            pattern = f"alert_zones_geo:*:{event_id}"
        else:
            pattern = "alert_zones_geo:*"
        return self.cache_manager.deletePattern(pattern)

    def invalidate_alert_zones_by_service(self, service_id: str) -> bool:
        cache_key = self._generate_cache_key("alert_zones_service", service_id)
        return self.cache_manager.deleteKey(cache_key)

    def invalidate_organization(self, org_id: str) -> bool:
        cache_key = self._generate_cache_key("organization", org_id)
        return self.cache_manager.deleteKey(cache_key)

    def invalidate_user_preferences(self, user_id: str) -> bool:
        cache_key = self._generate_cache_key("user_prefs", user_id)
        return self.cache_manager.deleteKey(cache_key)

    def invalidate_all_user_caches(self) -> int:
        patterns = ["users_by_org:*", "user_prefs:*"]
        total_deleted = 0
        for pattern in patterns:
            total_deleted += self.cache_manager.deletePattern(pattern)
        return total_deleted

    def invalidate_all_alert_zone_caches(self) -> int:
        patterns = ["alert_zones_geo:*", "alert_zones_service:*"]
        total_deleted = 0
        for pattern in patterns:
            total_deleted += self.cache_manager.deletePattern(pattern)
        return total_deleted

    def get_cache_stats(self) -> Dict[str, Any]:
        try:
            info = self.cache_manager.redisClient.info()
            return {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory_human': info.get('used_memory_human', 'N/A'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': self._calculate_hit_rate(info.get('keyspace_hits', 0), info.get('keyspace_misses', 0))
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {}

    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        total = hits + misses
        if total == 0:
            return 0.0
        return round((hits / total) * 100, 2)

    def test_connection(self) -> bool:
        return self.cache_manager.testConnection()

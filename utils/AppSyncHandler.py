import os
import json
import requests

APP_SYNC_URL = os.getenv("APP_SYNC_URL", "https://tmeo67xkzzccncxp3yywd2vlle.appsync-api.us-east-2.amazonaws.com/graphql")
APP_SYNC_API_KEY = os.getenv("APP_SYNC_API_KEY", "da2-ll3y5q2wafhqzgzu7b2dj2wx2m")

class AppSyncHandler:
	async def publish_message(self, topic, data):
		# Here you would actually send the notification
		url = APP_SYNC_URL
		apiKey = APP_SYNC_API_KEY
		headers = {
			'Content-Type': 'application/json',
			'x-api-key': apiKey,
			}
		
		mutationQuery = """
		mutation publish($name: String!, $data: AWSJSON!) {
		publish(name: $name, data: $data) {
			name
			data
		}
		}
		"""

		variables = {
		"name": topic,
		"data": json.dumps(data)
		}

		payload = {
			"query": mutationQuery,
			"variables": variables
		} 
		
		response = requests.post(url, json=payload, headers=headers)

		if response.status_code == 200:
			data = response.json()
		else:
			print(f"Failed to submit data: {response.status_code}")

import redis
import json
import logging
from typing import Optional, Any, List, Dict
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Replace with your Valkey endpoint
endpoint = "clustercfg.coddn-test.9nthzl.use2.cache.amazonaws.com"
port = 6379  # Default port

class CacheManager:
    redisClient = redis.Redis(host=endpoint, port=port, ssl=True, decode_responses=True)

    def testConnection(self):
        try:
            pong = self.redisClient.ping()
            logger.info(f"Cache connection test successful: {pong}")
            return True
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Error connecting to Valkey: {e}")
            return False

    def setKey(self, key: str, value: str, ttl: int = 3600):
        try:
            self.redisClient.set(key, value, ex=ttl)
            logger.debug(f"Set key '{key}' with TTL {ttl}s")
            return True
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Error setting key '{key}': {e}")
            return False

    def getKey(self, key: str) -> Optional[str]:
        try:
            value = self.redisClient.get(key)
            if value:
                logger.debug(f"Cache hit for key: {key}")
            else:
                logger.debug(f"Cache miss for key: {key}")
            return value
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Error getting key '{key}': {e}")
            return None

    def deleteKey(self, key: str):
        try:
            result = self.redisClient.delete(key)
            logger.debug(f"Deleted key '{key}', result: {result}")
            return result
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Error deleting key '{key}': {e}")
            return False

    def setJson(self, key: str, value: Any, ttl: int = 3600):
        try:
            json_value = json.dumps(value, default=str)
            return self.setKey(key, json_value, ttl)
        except (TypeError, ValueError) as e:
            logger.error(f"Error serializing value for key '{key}': {e}")
            return False

    def getJson(self, key: str) -> Optional[Any]:
        try:
            value = self.getKey(key)
            if value:
                return json.loads(value)
            return None
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"Error deserializing value for key '{key}': {e}")
            return None

    def setList(self, key: str, values: List[Any], ttl: int = 3600):
        try:
            json_values = json.dumps(values, default=str)
            return self.setKey(key, json_values, ttl)
        except (TypeError, ValueError) as e:
            logger.error(f"Error serializing list for key '{key}': {e}")
            return False

    def getList(self, key: str) -> Optional[List[Any]]:
        try:
            value = self.getKey(key)
            if value:
                return json.loads(value)
            return None
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"Error deserializing list for key '{key}': {e}")
            return None

    def deletePattern(self, pattern: str):
        try:
            keys = self.redisClient.keys(pattern)
            if keys:
                result = self.redisClient.delete(*keys)
                logger.debug(f"Deleted {result} keys matching pattern '{pattern}'")
                return result
            return 0
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Error deleting pattern '{pattern}': {e}")
            return 0

    def exists(self, key: str) -> bool:
        try:
            return bool(self.redisClient.exists(key))
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Error checking existence of key '{key}': {e}")
            return False

    def getTtl(self, key: str) -> int:
        try:
            return self.redisClient.ttl(key)
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Error getting TTL for key '{key}': {e}")
            return -1

    def extendTtl(self, key: str, additional_seconds: int):
        try:
            current_ttl = self.getTtl(key)
            if current_ttl > 0:
                new_ttl = current_ttl + additional_seconds
                return self.redisClient.expire(key, new_ttl)
            return False
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Error extending TTL for key '{key}': {e}")
            return False
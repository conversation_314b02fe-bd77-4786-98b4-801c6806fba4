version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 399444019738.dkr.ecr.us-east-2.amazonaws.com
      - echo Configuring AWS CLI
      - aws configure set aws_access_key_id ********************
      - aws configure set aws_secret_access_key hn1tV1KnTsT+bo/QGAQ9osp4L5/KHBOgX/2Mk/JZ
      - aws configure set region us-east-2
      - aws eks update-kubeconfig --region us-east-2 --name coddn
      - docker login --username alexmedina443 --password ************************************
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build --build-arg ENVIRONMENT=dev -t development/alert-distributor-dev .
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker image to ECR...
      - echo Build completed on `date`
      - IMAGE_TAG=$(date +%Y%m%d%H%M%S)
      - echo Pushing the Docker image to ECR with tag $IMAGE_TAG...
      - docker tag development/alert-distributor-dev:latest 399444019738.dkr.ecr.us-east-2.amazonaws.com/development/alert-distributor-dev:$IMAGE_TAG
      - docker push 399444019738.dkr.ecr.us-east-2.amazonaws.com/development/alert-distributor-dev:$IMAGE_TAG
      - kubectl apply -f deployment-files/deployment-development.yaml
      - kubectl set image deployment/alert-distributor-dev alert-distributor-dev=399444019738.dkr.ecr.us-east-2.amazonaws.com/development/alert-distributor-dev:$IMAGE_TAG -n development
artifacts:
  files:
    - '**/*'
  discard-paths: yes
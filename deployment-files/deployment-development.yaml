---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alert-distributor-dev
  namespace: development
  labels:
    app: alert-distributor-dev
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: alert-distributor-dev
  template:
    metadata:
      labels:
        app: alert-distributor-dev
    spec:
      containers:
        - name: alert-distributor-dev
          image: 399444019738.dkr.ecr.us-east-2.amazonaws.com/development/alert-distributor-dev:latest
          ports:
            - containerPort: 3001
          env:
            - name: ENV
              value: dev
            - name: NOTIFICATION_TOPIC
              value: NOTIFICATION_DEV
            - name: IOT_TOPIC
              value: IOT_DEV
            - name: MONGODB_CONNECTION_STRING
              value: "mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnDev"
            - name: DATABASE_NAME
              value: "coddn"
            - name: GROUP_ID
              value: devGroupAlertDistributor
            - name: RID_UI_TOPIC
              value: DETECTION_DEV
            - name: RID_TOPIC
              value: RID_DEV
            - name: ACCESS_KEY
              value: ********************
            - name: SECRET_KEY
              value: tRYGtPORKiKISf2F+7kkmhPCfXEnBkCCSyzlTeNt
            - name: REGION
              value: us-east-2
            - name: BOOTSTRAP_SERVERS
              value: "b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198"
            - name: APP_SYNC_URL
              value: https://tmeo67xkzzccncxp3yywd2vlle.appsync-api.us-east-2.amazonaws.com/graphql
            - name: APP_SYNC_API_KEY
              value: da2-ll3y5q2wafhqzgzu7b2dj2wx2m
            - name: ENV_URL
              value: https://dev.aerodefense.tech

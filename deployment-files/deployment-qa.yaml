---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alert-distributor-qa
  namespace: coddn-qa
  labels:
    app: alert-distributor-qa
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: alert-distributor-qa
  template:
    metadata:
      labels:
        app: alert-distributor-qa
    spec:
      containers:
        - name: alert-distributor-qa
          image: 399444019738.dkr.ecr.us-east-2.amazonaws.com/qa/alert-distributor-qa:latest
          ports:
            - containerPort: 3002
          env:
            - name: ENV
              value: qa
            - name: IOT_TOPIC
              value: IOT_QA
            - name: MONGODB_CONNECTION_STRING
              value: "mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnDev"
            - name: DATABASE_NAME
              value: "coddnQA"
            - name: GROUP_ID
              value: qaGroupAlertDistributor
            - name: NOTIFICATION_TOPIC
              value: NOTIFICATION_QA
            - name: RID_UI_TOPIC
              value: DETECTION_QA
            - name: RID_TOPIC
              value: RID_QA
            - name: ACCESS_KEY
              value: ********************
            - name: SECRET_KEY
              value: tRYGtPORKiKISf2F+7kkmhPCfXEnBkCCSyzlTeNt
            - name: REGION
              value: us-east-2
            - name: BOOTSTRAP_SERVERS
              value: "b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198"
            - name: APP_SYNC_URL
              value: https://3ehud5ztijb4rdwy3l3f4iblba.appsync-api.us-east-2.amazonaws.com/graphql
            - name: APP_SYNC_API_KEY
              value: da2-ylyaaxohxre2bfthzifptrsnw4
            - name: ENV_URL
              value: https://qa.aerodefense.tech
            - name: ENABLE_CACHE
              value: "true"
